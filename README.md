# AI Image Analyzer

Un SaaS semplice e funzionale per l'analisi di immagini tramite AI, costruito con Wasp e deployabile su Netlify.

🚀 **Stack**: React + Node.js + PostgreSQL + Wasp Framework
🤖 **AI**: Google Gemini 2.5 Flash Image Preview via OpenRouter
💳 **Pagamenti**: Stripe (€1 per analisi)
🔐 **Auth**: Google OAuth

## Funzionalità

- ✅ **Autenticazione Google OAuth** - Login rapido e sicuro
- ✅ **Pagamenti Stripe** - €1 per ogni analisi immagine
- ✅ **AI Image Analysis** - Powered by Google Gemini 2.5 Flash
- ✅ **Upload Immagini** - Supporta JPG, PNG, GIF, WebP
- ✅ **Download Risultati** - Salva le analisi come file di testo
- ✅ **Sistema di Controllo** - Un pagamento = una generazione
- ✅ **Responsive Design** - Funziona su desktop e mobile
- ✅ **Deploy Netlify** - Configurazione pronta per il deployment

## Quick Start

### Prerequisiti
- Node.js 20+
- Wasp CLI: `curl -sSL https://get.wasp-lang.sh/installer.sh | sh`

### Installazione
```bash
# Installa dipendenze
npm install

# Configura le variabili d'ambiente
cp .env.server.example .env.server
cp .env.client.example .env.client

# Avvia il database
wasp start db

# Migra il database (in un altro terminale)
wasp db migrate-dev

# Avvia l'applicazione
wasp start
```

L'app sarà disponibile su http://localhost:3000

## Configurazione

Vedi [SETUP.md](SETUP.md) per istruzioni dettagliate di configurazione.

## Deployment

Vedi [DEPLOYMENT.md](DEPLOYMENT.md) per istruzioni complete di deployment su Netlify.

## Costi Stimati (Versione Gratuita)

- **Netlify**: €0 (100GB bandwidth/mese)
- **Database**: €0 (Supabase free tier)
- **Stripe**: 2.9% + €0.25 per transazione
- **OpenRouter**: ~€0.01 per analisi
- **Google OAuth**: €0

**Totale per 100 analisi/mese**: ~€4-5

## Tecnologie

- **Frontend**: React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Node.js, Prisma ORM
- **Database**: PostgreSQL
- **Framework**: Wasp
- **Pagamenti**: Stripe
- **AI**: OpenRouter + Google Gemini
- **Auth**: Google OAuth
- **Deploy**: Netlify

## Supporto

- 📖 **Setup**: [SETUP.md](SETUP.md)
- 🚀 **Deployment**: [DEPLOYMENT.md](DEPLOYMENT.md)
- 🐝 **Wasp Docs**: https://wasp.sh/docs

## Licenza

MIT License

