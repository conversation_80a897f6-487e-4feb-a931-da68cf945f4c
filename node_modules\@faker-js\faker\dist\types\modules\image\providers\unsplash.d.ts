import type { Faker } from '../../..';
/**
 * Mo<PERSON>le to generate links to random images on `https://source.unsplash.com/`.
 *
 * The images generated from this module are fetched from an external service outside the control of Faker and could occasionally contain URLs which are broken or point to unexpected, disturbing, or offensive images. The service may also be subject to usage limits.
 *
 * @deprecated Use `faker.image` instead.
 */
export declare class Unsplash {
    private readonly faker;
    constructor(faker: <PERSON>aker);
    /**
     * Generates a new unsplash image url for a random supported category.
     *
     * These images are fetched from an external service outside the control of Faker and could occasionally contain URLs which point to unexpected, disturbing, or offensive images. Usage limits may contribute to this behavior.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     *
     * @deprecated Use `faker.image` instead.
     */
    image(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url.
     *
     * These images are fetched from an external service outside the control of Faker and could occasionally contain URLs which point to unexpected, disturbing, or offensive images. Usage limits may contribute to this behavior.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param category The category of the image to generate.
     * @param keyword The image keywords to use.
     *
     * @deprecated Use `faker.image` instead.
     */
    imageUrl(width?: number, height?: number, category?: string, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "food" category.
     *
     * These images are fetched from an external service outside the control of Faker and could occasionally contain URLs which point to unexpected, disturbing, or offensive images. Usage limits may contribute to this behavior.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     *
     * @deprecated Use `faker.image` instead.
     */
    food(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "people" category.
     *
     * These images are fetched from an external service outside the control of Faker and could occasionally contain URLs which point to unexpected, disturbing, or offensive images. Usage limits may contribute to this behavior.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     *
     * @deprecated Use `faker.image` instead.
     */
    people(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "nature" category.
     *
     * These images are fetched from an external service outside the control of Faker and could occasionally contain URLs which point to unexpected, disturbing, or offensive images. Usage limits may contribute to this behavior.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     *
     * @deprecated Use `faker.image` instead.
     */
    nature(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "technology" category.
     *
     * These images are fetched from an external service outside the control of Faker and could occasionally contain URLs which point to unexpected, disturbing, or offensive images. Usage limits may contribute to this behavior.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     *
     * @deprecated Use `faker.image` instead.
     */
    technology(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "objects" category.
     *
     * These images are fetched from an external service outside the control of Faker and could occasionally contain URLs which point to unexpected, disturbing, or offensive images. Usage limits may contribute to this behavior.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     *
     * @deprecated Use `faker.image` instead.
     */
    objects(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "buildings" category.
     *
     * These images are fetched from an external service outside the control of Faker and could occasionally contain URLs which point to unexpected, disturbing, or offensive images. Usage limits may contribute to this behavior.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     *
     * @deprecated Use `faker.image` instead.
     */
    buildings(width?: number, height?: number, keyword?: string): string;
}
