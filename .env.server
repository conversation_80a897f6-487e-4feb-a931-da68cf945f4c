# Database URL - Wasp gestisce automaticamente il database locale con `wasp start db`
# Per produzione, usa un database PostgreSQL esterno (es. Supabase, Railway, etc.)
# DATABASE_URL=postgresql://username:password@host:port/database

# Stripe Configuration
STRIPE_API_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_CUSTOMER_PORTAL_URL=https://billing.stripe.com/p/login/test_your_portal_url

# OpenRouter API Configuration
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key_here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Payment Plan IDs (crea questi prodotti in Stripe)
PAYMENTS_IMAGE_GENERATION_PLAN_ID=price_your_stripe_price_id_here

# Admin Emails (comma-separated list)
ADMIN_EMAILS=<EMAIL>

# Site URL (per OpenRouter headers)
SITE_URL=https://your-app-name.netlify.app

# Email Configuration (opzionale - per ora usa Dummy provider)
# SENDGRID_API_KEY=your_sendgrid_api_key_here
