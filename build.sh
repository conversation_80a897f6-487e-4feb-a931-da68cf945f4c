#!/bin/bash

# Build script per Netlify
echo "Starting Wasp build process..."

# Installa Wasp CLI se non presente
if ! command -v wasp &> /dev/null; then
    echo "Installing Wasp CLI..."
    curl -sSL https://get.wasp-lang.sh/installer.sh | sh
    export PATH="$HOME/.local/bin:$PATH"
fi

# Verifica che Wasp sia installato
wasp version

# Build dell'applicazione
echo "Building Wasp application..."
wasp build

echo "Build completed successfully!"
