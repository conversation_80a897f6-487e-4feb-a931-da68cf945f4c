import { <PERSON><PERSON><PERSON>cle, Image, Zap, Shield, Download } from 'lucide-react';
import { Link as WaspRouterLink, routes } from 'wasp/client/router';
import { useAuth } from 'wasp/client/auth';
import { Button } from '../components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardTitle } from '../components/ui/card';

const PricingPage = () => {
  const { data: user } = useAuth();


  return (
    <div className="bg-background text-foreground">
      <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
        {/* Header */}
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-base font-semibold leading-7 text-primary">Pricing</h2>
          <p className="mt-2 text-4xl font-bold tracking-tight text-foreground sm:text-5xl">
            Simple, transparent pricing
          </p>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Pay only when you need AI image analysis. No subscriptions, no hidden fees.
          </p>
        </div>

        {/* Pricing Card */}
        <div className="mx-auto mt-16 max-w-2xl">
          <Card className="relative overflow-hidden border-2 border-primary/20 shadow-lg">
            <div className="absolute inset-x-0 top-0 h-1 bg-gradient-to-r from-primary to-primary/60"></div>
            <CardContent className="p-8">
              <div className="text-center">
                <div className="flex items-center justify-center mb-4">
                  <Image className="h-12 w-12 text-primary" />
                </div>
                <h3 className="text-2xl font-bold text-foreground">Pay Per Analysis</h3>
                <div className="mt-4 flex items-baseline justify-center">
                  <span className="text-5xl font-bold tracking-tight text-foreground">€1</span>
                  <span className="ml-1 text-xl font-semibold text-muted-foreground">per image</span>
                </div>
                <p className="mt-6 text-lg leading-8 text-muted-foreground">
                  Get detailed AI-powered analysis of your images using Google Gemini 2.5 Flash
                </p>
              </div>

              {/* Features */}
              <div className="mt-8 space-y-4">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-foreground">Advanced AI image analysis</span>
                </div>
                <div className="flex items-center">
                  <Zap className="h-5 w-5 text-yellow-500 mr-3" />
                  <span className="text-foreground">Instant results in seconds</span>
                </div>
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-blue-500 mr-3" />
                  <span className="text-foreground">Secure payment via Stripe</span>
                </div>
                <div className="flex items-center">
                  <Download className="h-5 w-5 text-purple-500 mr-3" />
                  <span className="text-foreground">Download analysis as text file</span>
                </div>
              </div>
            </CardContent>

            <CardFooter className="p-8 pt-0">
              <Button size="lg" className="w-full" asChild>
                {user ? (
                  <WaspRouterLink to={routes.ImageGeneratorRoute.to}>
                    Start Analyzing Images
                  </WaspRouterLink>
                ) : (
                  <WaspRouterLink to={routes.SignupRoute.to}>
                    Get Started
                  </WaspRouterLink>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>

        {/* FAQ Section */}
        <div className="mx-auto mt-24 max-w-4xl">
          <h3 className="text-2xl font-bold text-center text-foreground mb-12">
            Frequently Asked Questions
          </h3>
          <div className="grid gap-8 md:grid-cols-2">
            <div>
              <h4 className="font-semibold text-foreground mb-2">How does payment work?</h4>
              <p className="text-muted-foreground">
                You pay €1 for each image analysis. Payment is processed securely through Stripe
                before the analysis begins.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-foreground mb-2">What AI model do you use?</h4>
              <p className="text-muted-foreground">
                We use Google Gemini 2.5 Flash Image Preview, one of the most advanced
                image analysis models available.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-foreground mb-2">Can I use it multiple times?</h4>
              <p className="text-muted-foreground">
                Yes! Each payment allows one image analysis. You can analyze as many images
                as you want by paying €1 for each.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-foreground mb-2">What formats are supported?</h4>
              <p className="text-muted-foreground">
                We support all common image formats including JPG, PNG, GIF, and WebP.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default PricingPage;
