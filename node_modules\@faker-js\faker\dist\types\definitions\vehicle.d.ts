import type { LocaleEntry } from './definitions';
/**
 * The possible definitions related to vehicles.
 */
export type VehicleDefinition = LocaleEntry<{
    /**
     * Some types of bicycles.
     */
    bicycle_type: string[];
    /**
     * Some types of fuel (e.g. `Gasoline`).
     */
    fuel: string[];
    /**
     * Some brands of manufactures (e.g. `Tesla`).
     */
    manufacturer: string[];
    /**
     * Some names of models (e.g. `Fiesta`).
     */
    model: string[];
    /**
     * Some types of vehicles (e.g. `Minivan`).
     */
    type: string[];
}>;
