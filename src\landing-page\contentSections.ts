import daBoiAvatar from '../client/static/da-boi.webp';
import kivo from '../client/static/examples/kivo.webp';
import messync from '../client/static/examples/messync.webp';
import microinfluencerClub from '../client/static/examples/microinfluencers.webp';
import promptpanda from '../client/static/examples/promptpanda.webp';
import reviewradar from '../client/static/examples/reviewradar.webp';
import scribeist from '../client/static/examples/scribeist.webp';
import searchcraft from '../client/static/examples/searchcraft.webp';
import { BlogUrl, DocsUrl } from '../shared/common';
import type { GridFeature } from './components/FeaturesGrid';

export const features: GridFeature[] = [
  {
    name: 'AI-Powered Analysis',
    description: 'Advanced image analysis using Google Gemini 2.5 Flash',
    emoji: '🤖',
    href: DocsUrl,
    size: 'large',
  },
  {
    name: 'Secure Payments',
    description: 'Safe €1 payments via Stripe',
    emoji: '🔐',
    href: DocsUrl,
    size: 'small',
  },
  {
    name: 'Google OAuth',
    description: 'Quick login with your Google account',
    emoji: '🚀',
    href: DocsUrl,
    size: 'small',
  },
  {
    name: 'Instant Results',
    description: 'Get detailed image descriptions in seconds',
    emoji: '⚡',
    href: DocsUrl,
    size: 'medium',
  },
  {
    name: 'Download Reports',
    description: 'Save your analysis results as text files',
    emoji: '📄',
    href: DocsUrl,
    size: 'medium',
  },
  {
    name: 'Fair Pricing',
    description: 'Pay only €1 per image analysis',
    emoji: '💸',
    href: DocsUrl,
    size: 'large',
  },
];

export const testimonials = [
  {
    name: 'Sarah M.',
    role: 'Digital Artist',
    avatarSrc: daBoiAvatar,
    socialUrl: '#',
    quote: "Perfect for analyzing my artwork and getting detailed descriptions for my portfolio!",
  },
  {
    name: 'Marco R.',
    role: 'Content Creator',
    avatarSrc: daBoiAvatar,
    socialUrl: '#',
    quote: 'The AI analysis helps me create better alt-text for my social media posts.',
  },
  {
    name: 'Lisa K.',
    role: 'Photographer',
    avatarSrc: daBoiAvatar,
    socialUrl: '#',
    quote: 'Amazing tool for understanding what makes my photos stand out!',
  },
];

export const faqs = [
  {
    id: 1,
    question: 'How much does it cost?',
    answer: 'Each image analysis costs just €1. You pay only when you want to analyze an image.',
    href: '#',
  },
  {
    id: 2,
    question: 'What AI model do you use?',
    answer: 'We use Google Gemini 2.5 Flash Image Preview via OpenRouter for advanced image analysis.',
    href: '#',
  },
  {
    id: 3,
    question: 'How do I sign up?',
    answer: 'Simply click "Get Started" and sign in with your Google account. No complex registration required.',
    href: '#',
  },
  {
    id: 4,
    question: 'What happens after I pay?',
    answer: 'After payment, you can upload your image and get an AI analysis. Each payment allows one analysis.',
    href: '#',
  },
  {
    id: 5,
    question: 'Can I download the results?',
    answer: 'Yes! You can download the AI-generated description as a text file for your records.',
    href: '#',
  },
];

export const footerNavigation = {
  app: [
    { name: 'Documentation', href: DocsUrl },
    { name: 'Blog', href: BlogUrl },
  ],
  company: [
    { name: 'About', href: 'https://wasp.sh' },
    { name: 'Privacy', href: '#' },
    { name: 'Terms of Service', href: '#' },
  ],
};

export const examples = [
  {
    name: 'Example #1',
    description: 'Describe your example here.',
    imageSrc: kivo,
    href: '#',
  },
  {
    name: 'Example #2',
    description: 'Describe your example here.',
    imageSrc: messync,
    href: '#',
  },
  {
    name: 'Example #3',
    description: 'Describe your example here.',
    imageSrc: microinfluencerClub,
    href: '#',
  },
  {
    name: 'Example #4',
    description: 'Describe your example here.',
    imageSrc: promptpanda,
    href: '#',
  },
  {
    name: 'Example #5',
    description: 'Describe your example here.',
    imageSrc: reviewradar,
    href: '#',
  },
  {
    name: 'Example #6',
    description: 'Describe your example here.',
    imageSrc: scribeist,
    href: '#',
  },
  {
    name: 'Example #7',
    description: 'Describe your example here.',
    imageSrc: searchcraft,
    href: '#',
  },
];
