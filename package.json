{"name": "opensaas", "type": "module", "dependencies": {"@aws-sdk/client-s3": "^3.523.0", "@aws-sdk/s3-presigned-post": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.523.0", "@google-analytics/data": "4.1.0", "@headlessui/react": "1.7.13", "@hookform/resolvers": "^5.1.1", "@lemonsqueezy/lemonsqueezy.js": "^3.2.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@stripe/react-stripe-js": "^4.0.2", "@stripe/stripe-js": "^7.9.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "apexcharts": "3.41.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "headlessui": "^0.0.0", "lucide-react": "^0.525.0", "node-fetch": "3.3.0", "openai": "^4.55.3", "prettier": "3.1.1", "prettier-plugin-tailwindcss": "0.5.11", "react": "^18.2.0", "react-apexcharts": "1.4.1", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.26.2", "stripe": "18.1.0", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.2.7", "tailwindcss-animate": "^1.0.7", "vanilla-cookieconsent": "^3.0.1", "wasp": "file:.wasp/out/sdk/wasp", "zod": "^3.25.76"}, "devDependencies": {"@faker-js/faker": "8.3.1", "@types/express": "^5.0.0", "@types/react": "^18.0.37", "prisma": "5.19.1", "typescript": "5.8.2", "vite": "^7.0.6"}}