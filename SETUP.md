# AI Image Analyzer - Setup Guide

Questo è un SaaS semplice per l'analisi di immagini usando AI, costruito con Wasp e deployabile su Netlify.

## Funzionalità

- ✅ Autenticazione Google OAuth
- ✅ Pagamento €1 tramite Stripe per ogni analisi
- ✅ Upload immagini e analisi AI tramite OpenRouter (Gemini 2.5 Flash Image Preview)
- ✅ Download delle descrizioni generate
- ✅ Sistema di controllo: un pagamento = una generazione
- ✅ Persistenza dei dati anche dopo refresh della pagina

## Setup Locale

### 1. Prerequisiti
- Node.js 20+
- npm
- Wasp CLI: `curl -sSL https://get.wasp-lang.sh/installer.sh | sh`

### 2. Installazione
```bash
npm install
```

### 3. Configurazione Variabili d'Ambiente

#### .env.server
Copia `.env.server` e configura:

**Stripe:**
1. Vai su https://dashboard.stripe.com/test/apikeys
2. Copia la Secret Key (sk_test_...)
3. Installa Stripe CLI: https://stripe.com/docs/stripe-cli
4. Esegui: `stripe listen --forward-to localhost:3001/payments-webhook`
5. Copia il webhook secret (whsec_...)
6. Crea un prodotto su Stripe per €1 e copia il Price ID

**Google OAuth:**
1. Vai su https://console.cloud.google.com/
2. Crea un nuovo progetto o seleziona uno esistente
3. Abilita Google+ API
4. Crea credenziali OAuth 2.0
5. Aggiungi `http://localhost:3000` agli URI autorizzati
6. Copia Client ID e Client Secret

**OpenRouter:**
1. Vai su https://openrouter.ai/
2. Crea un account e ottieni la API key
3. Assicurati di avere crediti per il modello `google/gemini-2.5-flash-image-preview`

#### .env.client
Configura la Stripe Publishable Key (pk_test_...)

### 4. Avvio Locale
```bash
# Avvia il database (in un terminale separato)
wasp start db

# Migra il database (in un altro terminale)
wasp db migrate-dev

# Avvia l'app
wasp start
```

L'app sarà disponibile su http://localhost:3000

## Deploy su Netlify

### 1. Preparazione
1. Fai il commit di tutti i file
2. Pusha su GitHub

### 2. Configurazione Netlify
1. Connetti il repository su Netlify
2. Configura le build settings:
   - Build command: `wasp build`
   - Publish directory: `.wasp/build/web-app/build`

### 3. Variabili d'Ambiente su Netlify
Aggiungi tutte le variabili da `.env.server` nelle Environment Variables di Netlify.

**Importante:** Aggiorna gli URL per produzione:
- Google OAuth: aggiungi il dominio Netlify agli URI autorizzati
- Stripe webhook: configura l'endpoint per il tuo dominio
- SITE_URL: imposta il tuo dominio Netlify

### 4. Database di Produzione
Per produzione, usa un database PostgreSQL esterno:
- Supabase (gratuito): https://supabase.com/
- Railway: https://railway.app/
- Neon: https://neon.tech/

Configura DATABASE_URL con la connection string del tuo database.

## Struttura del Progetto

```
├── main.wasp                 # Configurazione principale Wasp
├── schema.prisma            # Schema database
├── src/
│   ├── image-generator/     # Logica generazione immagini
│   ├── auth/               # Configurazione autenticazione
│   ├── payment/            # Logica pagamenti Stripe
│   └── components/         # Componenti UI
├── netlify.toml            # Configurazione Netlify
├── .env.server             # Variabili ambiente server
└── .env.client             # Variabili ambiente client
```

## Costi Stimati (Versione Gratuita Netlify)

- Netlify: Gratuito (100GB bandwidth, 300 build minutes/mese)
- Database: Gratuito (Supabase free tier)
- Stripe: 2.9% + €0.25 per transazione
- OpenRouter: ~$0.01 per analisi immagine
- Google OAuth: Gratuito

## Limitazioni Versione Gratuita

- Netlify: 100GB bandwidth/mese
- Supabase: 500MB storage, 2GB transfer
- Build time: 300 minuti/mese su Netlify

## Supporto

Per problemi o domande, controlla:
1. Documentazione Wasp: https://wasp.sh/docs
2. Documentazione Netlify: https://docs.netlify.com/
3. Documentazione Stripe: https://stripe.com/docs
