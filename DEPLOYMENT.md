# Deployment Guide - AI Image Analyzer

## Checklist Pre-Deployment

### 1. Configurazione Stripe
- [ ] Crea account Stripe: https://dashboard.stripe.com/
- [ ] Ottieni le chiavi API (Secret Key e Publishable Key)
- [ ] Crea un prodotto per €1 e ottieni il Price ID
- [ ] Configura webhook endpoint per il tuo dominio
- [ ] Testa con carte di credito di test

### 2. Configurazione Google OAuth
- [ ] Vai su Google Cloud Console: https://console.cloud.google.com/
- [ ] Crea un nuovo progetto o seleziona esistente
- [ ] Abilita Google+ API
- [ ] Crea credenziali OAuth 2.0
- [ ] Aggiungi domini autorizzati (localhost + dominio produzione)
- [ ] Ottieni Client ID e Client Secret

### 3. Configurazione OpenRouter
- [ ] Registrati su OpenRouter: https://openrouter.ai/
- [ ] Ottieni API Key
- [ ] Verifica crediti per il modello `google/gemini-2.5-flash-image-preview`
- [ ] Testa l'API con una chiamata di prova

### 4. Database di Produzione
- [ ] Crea database PostgreSQL (consigliato: Supabase gratuito)
- [ ] Ottieni connection string
- [ ] Testa connessione

## Deployment su Netlify

### Step 1: Preparazione Repository
```bash
git add .
git commit -m "Initial commit - AI Image Analyzer"
git push origin main
```

### Step 2: Configurazione Netlify
1. Vai su https://netlify.com/
2. Clicca "New site from Git"
3. Connetti il tuo repository GitHub
4. Configura build settings:
   - Build command: `./build.sh`
   - Publish directory: `.wasp/build/web-app/build`

### Step 3: Variabili d'Ambiente
Aggiungi queste variabili in Netlify > Site settings > Environment variables:

```
# Database
DATABASE_URL=postgresql://user:password@host:port/database

# Stripe
STRIPE_API_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_CUSTOMER_PORTAL_URL=https://billing.stripe.com/p/login/your_portal

# OpenRouter
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Payment Plans
PAYMENTS_IMAGE_GENERATION_PLAN_ID=price_your_stripe_price_id

# Admin
ADMIN_EMAILS=<EMAIL>

# Site
SITE_URL=https://your-app-name.netlify.app
```

### Step 4: Configurazione Client
Aggiungi in Netlify > Site settings > Environment variables:
```
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
```

### Step 5: Deploy
1. Clicca "Deploy site"
2. Attendi il completamento del build
3. Testa l'applicazione

## Post-Deployment

### 1. Configurazione Webhook Stripe
1. Vai su Stripe Dashboard > Webhooks
2. Aggiungi endpoint: `https://your-app.netlify.app/payments-webhook`
3. Seleziona eventi: `payment_intent.succeeded`, `payment_intent.payment_failed`
4. Copia il webhook secret e aggiornalo nelle variabili d'ambiente

### 2. Aggiornamento Google OAuth
1. Vai su Google Cloud Console
2. Aggiungi il tuo dominio Netlify agli URI autorizzati
3. Testa il login

### 3. Test Completo
- [ ] Registrazione con Google
- [ ] Pagamento €1 con carta di test
- [ ] Upload immagine
- [ ] Generazione analisi
- [ ] Download risultato

## Troubleshooting

### Build Fails
- Verifica che tutte le dipendenze siano installate
- Controlla i log di build per errori specifici
- Assicurati che Wasp CLI sia installato correttamente

### Payment Issues
- Verifica le chiavi Stripe (test vs live)
- Controlla che il webhook sia configurato correttamente
- Verifica che il Price ID sia corretto

### OAuth Issues
- Controlla che i domini siano autorizzati in Google Console
- Verifica Client ID e Secret
- Assicurati che Google+ API sia abilitata

### Database Issues
- Verifica la connection string
- Controlla che il database sia accessibile
- Esegui le migrazioni se necessario

## Monitoraggio

### Metriche da Monitorare
- Numero di registrazioni
- Pagamenti completati
- Errori API OpenRouter
- Tempo di risposta

### Log Utili
- Netlify Function logs
- Stripe Dashboard events
- OpenRouter usage statistics

## Costi Stimati (Mensili)

### Versione Gratuita
- Netlify: €0 (fino a 100GB bandwidth)
- Supabase: €0 (fino a 500MB storage)
- Stripe: 2.9% + €0.25 per transazione
- OpenRouter: ~€0.01 per analisi
- Google OAuth: €0

### Totale per 100 analisi/mese: ~€4-5

## Scaling

### Per aumentare i limiti:
1. **Netlify Pro** (€19/mese): 1TB bandwidth, build minutes illimitati
2. **Supabase Pro** (€25/mese): 8GB storage, 100GB transfer
3. **Database dedicato**: Railway, PlanetScale, o AWS RDS

## Supporto

Per problemi tecnici:
1. Controlla i log di Netlify
2. Verifica le configurazioni API
3. Testa in locale prima del deploy
4. Consulta la documentazione Wasp: https://wasp.sh/docs
